<template>
  <div class="price_item_wrapper" @click="$emit('item-click', priceItem)">
    <div class="company_name_and_city">
      <div class="company_name">{{ priceItem.companyName }}</div>
      <div class="date">{{ dateText }}</div>
    </div>
    <div class="price">{{ priceItem.price }}{{ priceItem.unit }}</div>
    <div class="minus_yesterday" :class="{ show_detail_label: detailChange }">
      <div :class="labelItem.colorClass">{{ labelItem.label }}</div>
      <div v-if="detailChange" class="detail_label">标准变更</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    priceItem: {
      type: Object,
      default: () => ({})
    },
    companyUpdated: {
      // 企业是否有过更新
      type: Boolean,
      default: false
    }
  },
  computed: {
    dateText() {
      return new Date(this.priceItem.updateTime).format('MM-dd hh:mm:ss')
    },
    detailChange() {
      // 判定标准是否变更
      return this.companyUpdated && this.priceItem.minusYesterday === 0
    },
    labelItem() {
      let label, colorClass
      if (this.priceItem.minusYesterday > 0) {
        label = `+${this.formatDifference(this.priceItem.minusYesterday)}`
        colorClass = 'up_color'
      } else if (this.priceItem.minusYesterday < 0) {
        label = `${this.formatDifference(this.priceItem.minusYesterday)}`
        colorClass = 'down_color'
      } else {
        label = '平'
        colorClass = 'same_color'
      }
      return {
        label,
        colorClass
      }
    }
  },
  methods: {
    formatDifference(value) {
      const isNegative = value < 0
      const positiveNumber = Math.abs(value)
      let formattedValue
      if (positiveNumber < 1) {
        formattedValue = (positiveNumber * 100).toFixed(4)
      } else {
        formattedValue = positiveNumber.toFixed(4)
      }
      const parts = formattedValue.split('.')
      const integerPart = parseInt(parts[0], 10)
      const decimalPart = parts[1] ? parts[1].replace(/0+$/, '') : ''
      const unit = positiveNumber < 1 ? '分' : '元'
      const result = (decimalPart ? integerPart + '.' + decimalPart : integerPart) + unit
      return isNegative ? '-' + result : result
    }
  }
}
</script>

<style lang="scss">
.price_item_wrapper {
  margin: 0 16px;
  border-bottom: 1px solid #f1f1f1;
  font-size: 14px;
  height: 60px;
  position: relative;
  .price {
    position: absolute;
    line-height: 60px;
    left: 50%;
    top: 0;
  }
  .company_name_and_city {
    width: 50%;
  }
  .company_name {
    line-height: 24px;
    padding-top: 9px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .date {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 20px;
    font-size: 12px;
    color: #999;
  }
  .minus_yesterday {
    position: absolute;
    top: 0;
    right: 0;
    padding: 20px 0;
    text-align: right;
    &.show_detail_label {
      padding: 10px 0;
    }
    .detail_label {
      color: #2ea5eb;
    }
  }
  .up_color {
    color: #f93a4a;
  }
  .down_color {
    color: #07c160;
  }
  .same_color {
    color: #999;
  }
}
</style>
