<template>
  <page-meta :page-style="pageStyle"></page-meta>
  <view class="page_root" :style="{ 'padding-top': `${paddingTopHeight}px` }">
    <ToolbarAlert />
    <template v-if="!canViewModule && sysConfig.forceShare === 1">
      <div class="access-module" :style="{ top: `${paddingTopHeight + 70}px` }"></div>
      <div class="share-alert">
        <img src="../../static/images/share-alert.png" alt="share-alert" />
        <button plain open-type="share"></button>
      </div>
      <!-- <div class="share-alert">
        <button class="primary-btn" style="margin-top: 1.25rem" @click="handleShare">
          查看价格走势
        </button>
      </div> -->
    </template>
    <navigate-bar :deviceInfo="deviceInfo">
      <div
        :style="{
          display: isMenuFixedToTop ? 'block' : 'none',
          height: activePriceIdx === 0 && areaMenus.length > 0 ? '100px' : '50px'
        }"
      ></div>
      <v-tabs
        v-model="activeCropIdx"
        :tabs="cropMenus"
        fontSize="16px"
        activeFontSize="20px"
        bold
        color="#787878"
        activeColor="#333"
        lineColor="#ff6a01"
        height="50px"
        lineHeight="3px"
        lineRadius="1px"
        padding="0 10px"
        paddingItem="0 13px"
        bgColor="transparent"
        :style="{ display: isMenuFixedToTop && !isTradeOrderTab ? 'none' : 'block' }"
        @change="onCropChange"
      ></v-tabs>
    </navigate-bar>
    <div class="show-popup-btn tap_right" @click="isPopupVisible = true">
      <image
        style="width: 36px; height: 36px"
        mode="aspectFit"
        :draggable="false"
        src="/static/images/wechat_group.svg"
      />粮价交流群
    </div>
    <Popup v-model="isPopupVisible" contentWidth="300px">
      <div class="popup-content">
        <image class="popup-content_bg" src="/static/images/wechat_group_bg.png" mode="widthFix" />
        <div class="popup-close-btn" @click="isPopupVisible = false">
          <icon type="cancel" size="32" color="#333" />
        </div>
        <image
          class="popup-content_qrcode"
          mode="aspectFit"
          show-menu-by-longpress
          :draggable="false"
          :src="sysConfig.groupQrCode"
        />
        <!-- <div class="popup-content_desc">
					加好友进粮食价格群
					<br />免费获得本地价格信息、专业分析。
				</div> -->
      </div>
    </Popup>
    <div v-show="!isTradeOrderTab">
      <StatsCard class="stats-card" ref="statsCard" :cropId="cropId" :data="statsData" />

      <view class="line_chart top">
        <l-echart ref="topChartRef"></l-echart>
      </view>
      <div class="gap"></div>
      <div
        class="area_menu_wrapper"
        :style="{ height: activePriceIdx === 0 && regionsData.length > 0 ? '100px' : '50px' }"
      >
        <view
          class="area_menu_bar"
          :style="{ top: isMenuFixedToTop ? `${deviceInfo.totalBarHeight}px` : '0' }"
          :class="{ fix: isMenuFixedToTop }"
        >
          <v-tabs
            v-model="activePriceIdx"
            :tabs="priceTypes"
            :scroll="false"
            fontSize="16px"
            activeFontSize="20px"
            bold
            color="#787878"
            activeColor="#333"
            lineColor="#ff6a01"
            height="50px"
            lineHeight="3px"
            lineRadius="1px"
            :lineScale="0.2"
            padding="0 10px"
            paddingItem="0 16px"
            bgColor="transparent"
            @change="onPriceTypeChange"
          ></v-tabs>
          <div
            :style="{ display: activePriceIdx === 0 && regionsData.length > 0 ? 'block' : 'none' }"
          >
            <v-tabs
              v-model="activeAreaIdx"
              :tabs="regionsData"
              field="name"
              fontSize="16px"
              activeFontSize="20px"
              bold
              color="#787878"
              activeColor="#333"
              lineColor="#ff6a01"
              height="50px"
              lineHeight="3px"
              lineRadius="1px"
              :lineScale="0.4"
              padding="0 10px"
              paddingItem="0 16px"
              bgColor="transparent"
              @change="onAreaChange"
            ></v-tabs>
          </div>
        </view>
      </div>
      <div v-show="activePriceIdx === 0">
        <div>
          <view class="line_chart bottom">
            <l-echart ref="bottomChartRef"></l-echart>
          </view>
        </div>
        <view class="factory_list_wrapper">
          <CompanyPrices
            :cropId="cropId"
            :cropName="cropMenus[activeCropIdx]"
            :companyMaps="companyMaps"
            :areaCompanyData="regionsData[activeAreaIdx]"
          />
        </view>
      </div>
      <div v-if="activePriceIdx === 1">
        <LocalPrices
          :cropId="cropId"
          :paddingTopHeight="paddingTopHeight"
          @isLocalPricesPopupVisible="val => (isLocalPricesPopupVisible = val)"
        />
      </div>
      <div v-if="activePriceIdx === 2">
        <News :cropId="cropId" :paddingTopHeight="paddingTopHeight" />
      </div>
    </div>
    <div v-show="isTradeOrderTab">
      <TradeOrders ref="tradeOrders" />
    </div>
    <PopupBottom v-model="isPickerPopupVisible">
      <picker-view
        class="picker-view"
        :indicator-style="indicatorStyle"
        :value="pickerValue"
        immediate-change
        @change="onColumnChange"
      >
        <picker-view-column v-for="(column, index) in cropExtraPickerArgs" :key="index">
          <view class="item" v-for="(item, j) in column" :key="j">{{ item }}</view>
        </picker-view-column>
      </picker-view>
      <view
        class="picker-action-content"
        :style="{ paddingBottom: `${deviceInfo.bottomInsetSafeArea}px` }"
      >
        <button @click="onPickerClose">取消</button>
        <button type="primary" @click="onPickerSubmit">确定</button>
      </view>
    </PopupBottom>
    <Popup v-model="isSharePopupVisible" contentWidth="320px">
      <div class="share-popup-close-btn" @click="isSharePopupVisible = false">
        <icon type="cancel" size="32" color="#333" />
      </div>
      <div class="share-popup">
        <div style="margin-bottom: 20px; color: #ff6a01">
          分享后每位好友打开可获得 {{ sysConfig.assistancePoints }} 积分
        </div>
        <div style="display: flex; align-items: center">
          剩余积分：<text :style="{ color: userInfo.points <= 3 ? '#ff5c5c' : '#333' }">{{
            userInfo.points
          }}</text>
          <div v-if="userInfo.points <= 0" class="refresh-btn" @click="fetchUserDataAndPoints">
            刷新
          </div>
        </div>
        <div style="margin-bottom: 20px">
          <p style="text-align: center">
            本次使用积分：<text>{{ currentUsedPoints }}</text>
          </p>
          <p style="font-size: 12px; text-align: center; color: #999">
            每日使用2积分后不再扣除积分
          </p>
        </div>

        <div class="share-popup-alert">
          <icon type="info" size="16" />积分为 0 时无法查看价格走势等内容
        </div>
        <button class="primary-btn" open-type="share">分享得积分</button>
      </div>
    </Popup>
  </view>
</template>

<script>
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'
import priceManager from '../../api/priceManager'
import userManager from '../../api/userManager'
import logManager from '../../api/logManager'
import configManager from '../../api/configManager'
import pointsManager from '../../api/pointsManager'
import Popup from '../../components/popup/index.vue'
import PopupBottom from '../../components/PopupBottom.vue'
import LocalPrices from './components/LocalPrices/index.vue'
import ToolbarAlert from './components/ToolbarAlert/index.vue'
import { ModuleAccessControl } from '../../utils/shareAccessControl'
const { formatDate } = require('../../utils/date')
import vTabs from '../../components/v-tabs/v-tabs.vue'
import handler from './handler'
import TradeOrders from './components/trade-orders.vue'
import CompanyPrices from './components/company-prices.vue'
import StatsCard from './components/stats-card.vue'
import News from '../news/index.vue'

const HUMIDITY_RANGE = [14, 40]
const HUMIDITY_RANGE_LENGTH = HUMIDITY_RANGE[1] - HUMIDITY_RANGE[0]
const cornPickerLabels = ['村里玉米棒', '村里玉米粒']
const cornPickerValues = Array.from(
  { length: HUMIDITY_RANGE_LENGTH + 1 },
  (_, index) => `出粒${index + HUMIDITY_RANGE[0]}水`
)
const cornPickerValues2 = Array.from(
  { length: HUMIDITY_RANGE_LENGTH + 1 },
  (_, index) => `测量${index + HUMIDITY_RANGE[0]}水`
)
const cropPickerValues = Array.from(
  { length: HUMIDITY_RANGE_LENGTH + 1 },
  (_, index) => `${index + HUMIDITY_RANGE[0]}水`
)

export default {
  components: {
    Popup,
    PopupBottom,
    ToolbarAlert,
    LocalPrices,
    vTabs,
    StatsCard,
    TradeOrders,
    CompanyPrices,
    News
  },
  data() {
    return {
      indicatorStyle: `height: 50px;`,
      deviceInfo: {
        statusBarHeight: 44,
        navBarHeight: 40,
        totalBarHeight: 84
      },
      adBoxHeight: 0,
      scrollFixTopValue: 0,
      isMenuFixedToTop: false,
      cropId: null,
      sysConfig: {
        groupQrCode: null, // 二维码地址
        qrCodeShowAuto: 1, // 是否自动展示二维码
        qrCodeNoRepeatHour: 24, // 多少个小时内不自动展示
        qrCodeShowCountMax: 6, // 每个客户最多展示多少次后不再自动弹窗
        showInfoModule: 0, // 是否显示资讯模块
        shareValidDays: 7, // 分享链接有效期，单位：天
        forceShare: 0 // 是否强制分享
      },
      isPickerPopupVisible: false,
      isPopupVisible: false,
      isLocalPricesPopupVisible: false,
      canViewModule: false,
      priceTypes: ['厂家价格', '本地价格'],
      activePriceIdx: 0,
      cropConfigs: [],
      cropMenus: [],
      activeCropIdx: -1,
      areaMenus: [],
      activeAreaIdx: -1,
      activeChart: '',
      _pickerValue: [],
      firstPickerValue: 0,
      pickerCacheValue: [],
      companyMaps: {},
      dateSeries: [],
      statsData: {},
      regionsData: [],
      userInfo: {},
      isSharePopupVisible: false,
      todayPointsUsedCount: 0,
      currentUsedPoints: 0,
      pointsMaxUsed: 2, // 当日扣除最大积分数后，查看模块不再扣除积分
      adDisplayCount: 0
    }
  },
  watch: {},
  computed: {
    pageStyle() {
      const {
        isPopupVisible,
        isLocalPricesPopupVisible,
        isPickerPopupVisible,
        canViewModule,
        isTradeOrderTab
      } = this
      const isAnyPopupVisible = isPopupVisible || isLocalPricesPopupVisible || isPickerPopupVisible
      const shouldHideOverflow =
        isAnyPopupVisible || (!canViewModule && this.sysConfig.forceShare === 1 && !isTradeOrderTab)
      return `overflow: ${shouldHideOverflow ? 'hidden' : 'visible'}`
    },
    paddingTopHeight() {
      return (this.deviceInfo.statusBarHeight || 44) + this.deviceInfo.navBarHeight + 50
    },
    isCornCrop() {
      return this.cropId === 1
    },
    cropExtraPickerArgs() {
      const cornPickerValueOptions = {
        0: cornPickerValues,
        1: cornPickerValues2
      }
      const columnLables = this.isCornCrop
        ? cornPickerLabels
        : [`${this.areaMenus[this.activeAreaIdx]?.name}村里`]
      const pickerColumnValues = this.isCornCrop
        ? cornPickerValueOptions[this.firstPickerValue]
        : cropPickerValues
      return [columnLables, pickerColumnValues]
    },
    pickerValue: {
      get() {
        if (
          !Array.isArray(this._pickerValue) ||
          this._pickerValue.length === 0 ||
          this._pickerValue == null
        ) {
          const defaultHumidity = this.cropConfigs[this.activeCropIdx]?.defaultHumidity || 0
          const adjustedHumidity = Math.max(defaultHumidity - 14, 0)
          return [0, adjustedHumidity]
        }
        return this._pickerValue
      },
      set(value) {
        this._pickerValue = value
      }
    },
    isTradeOrderTab() {
      return this.activeCropIdx === this.cropMenus.length - 1
    },
    xAxis() {
      return this.dateSeries.map(item => formatDate(new Date(item), 'MM/dd')) || []
    },
    chartSeries() {
      return this.regionsData.map(item => ({
        name: item.name,
        regionId: item.id,
        type: 'line',
        data: item.averageArr
      }))
    }
  },

  async created() {
    this.deviceInfo = await configManager.getDeviceInfo()
    await this.fetchCropConfigs()
    this.onCropChange()
  },

  onShow() {
    if (this.$refs.statsCard) {
      this.$refs.statsCard.loadStatsData()
    }
  },

  async onReady() {
    await this.$nextTick()
    await this.updateScrollFixTop()
  },

  onCreatedSysConfigLogin() {
    // this.fetchUserDataAndPoints()
    const sysConfig = configManager.getSysConfig() || {}
    const oldForceShare = this.sysConfig?.forceShare
    this.sysConfig = {
      ...this.sysConfig,
      ...sysConfig,
      groupQrCode: `${sysConfig.groupQrCode}?timestamp=${Date.now()}`
    }
    const { forceShare, shareValidDays, showInfoModule } = this.sysConfig
    // 当 forceShare 从 1 变为 0 时，清除分享状态
    if (oldForceShare === 1 && forceShare === 0) ModuleAccessControl.clearShareState()

    if (forceShare === 1) this.checkModuleAccess(shareValidDays || 7)
    if (showInfoModule === 1) this.priceTypes.push('资讯')
    this.onQrCodeDisplay()
  },

  onPageScroll(event) {
    if (!this.isTradeOrderTab) {
      const shouldFix = event.scrollTop >= this.scrollFixTopValue - 1
      if (this.isMenuFixedToTop != shouldFix) {
        this.isMenuFixedToTop = shouldFix
      }
    }
  },
  async onShareAppMessage() {
    this.isSharePopupVisible = false
    setTimeout(() => {
      ModuleAccessControl.recordShare() // 记录分享成功
      this.checkModuleAccess(this.sysConfig.shareValidDays)
      console.log(`分享成功，用户可以在 ${this.sysConfig.shareValidDays} 天内访问模块内容`)
    }, 2000)
    const path = await userManager.getSharePath('/pages/index/index')
    return {
      title: '千万仓-玉米、小麦价格',
      imageUrl: '../../static/images/share-img.jpg',
      path
    }
  },
  methods: {
    onLoginSuccess() {},

    async fetchCropConfigs() {
      try {
        const res = await priceManager.queryPriceConfigs()
        this.cropConfigs = res.sort((a, b) => a.sortNumber - b.sortNumber)
        this.cropMenus = this.cropConfigs.map(item => item.name).concat(['成交价格'])
        this.activeCropIdx = 0
      } catch (error) {
        console.error('[ fetchCropConfigs ]', error)
        throw error
      }
    },
    async loadChartData() {
      const regionIds = this.areaMenus.map(item => item.id).join(',')
      try {
        const resData = await priceManager.queryPriceChartData(this.cropId, { regionIds })
        this.dateSeries = handler.generateDates(resData.dateRange[0], resData.dateRange[1])
        this.companyMaps = resData.companyMap
        this.regionsData = resData.regionDataList
        this.statsData = resData.todayDetail
        return true
      } catch (error) {
        console.error('[ loadChartData ]', error)
        throw error
      }
    },

    async onQrCodeDisplay() {
      try {
        if (this.sysConfig.qrCodeShowAuto) {
          const myLogDetail = await userManager.queryGroupQrCodeLatestLog()
          if (myLogDetail && myLogDetail.total < this.sysConfig.qrCodeShowCountMax) {
            await logManager.addAutoViewQrCodeLog()
            this.isPopupVisible = true
          }
          /* const shouldRecord = uni.getStorageSync('shouldRecord') || 'enable'
						// 用户的二维码展示次数未超过指定的次数
						if (myLogDetail && myLogDetail.total < this.sysConfig.qrCodeShowCountMax) {
							// TODO: 判断用户是否存在最近的记录，如果存在是否是在 sysConfig.qrCodeNoRepeatHour 周期内
							const currentTime = new Date().getTime()
							const latestRecordTime = myLogDetail.latestRecord?.createTime
							if (
								!latestRecordTime ||
								this.isIntervalMoreThanHours(
									latestRecordTime,
									currentTime,
									this.sysConfig.qrCodeNoRepeatHour
								)
							) {
								if (shouldRecord === 'enable') {
									await logManager.addAutoViewQrCodeLog()
									this.isPopupVisible = true
									uni.setStorageSync('shouldRecord', 'disable')
								} else {
									uni.setStorageSync('shouldRecord', 'enable')
								}
							}
						} */
        }
      } catch (error) {
        console.error('[ onQrCodeDisplay ]', error)
        throw error
      }
    },

    checkModuleAccess(validDays) {
      if (ModuleAccessControl.canAccessModule(validDays, 3)) {
        this.canViewModule = true
        console.log('%c [ 用户可以查看模块内容 ] ', 'background:pink; color:#bf2c9f;')
      } else {
        this.canViewModule = false
        console.log('%c [ 需要用户分享后才能查看 ] ', 'background:pink; color:#bf2c9f;')
      }
    },

    async fetchUserDataAndPoints() {
      this.currentUsedPoints = 0
      try {
        const [userInfo, todayPointsUsedCount] = await Promise.all([
          userManager.refreshUser(),
          pointsManager.getTodayUsed()
        ])
        this.userInfo = userInfo
        this.todayPointsUsedCount = todayPointsUsedCount

        if (userInfo.points <= 0 && todayPointsUsedCount < this.pointsMaxUsed) {
          this.isSharePopupVisible = true
          return
        }

        if (todayPointsUsedCount < this.pointsMaxUsed) {
          const params = {
            transactionType: 'use',
            point: 1,
            description: '查看价格走势'
          }
          await pointsManager.addRecord(params)
          this.todayPointsUsedCount += 1
          this.userInfo.points -= 1
          this.currentUsedPoints = 1
        }
        this.canViewModule = true
        if (userInfo.points <= 5) {
          this.isSharePopupVisible = true
        }
      } catch (error) {
        console.error('Error fetching user data and points:', error)
        throw error
      }
    },

    async handleShare() {
      try {
        await this.fetchUserDataAndPoints()
      } catch (error) {
        console.error('[ handleShare ]', error)
      }
    },

    async onCropChange(index = 0) {
      try {
        this.activeCropIdx = index
        if (this.isTradeOrderTab) {
          // 已经拉取过交易数据
          this.$refs.tradeOrders.tradeTotal < 0 && this.$refs.tradeOrders.loadData()
          return
        }
        if (this.cropConfigs.length === 0) return
        const selectedCropConfig = this.cropConfigs[this.activeCropIdx]
        this.cropId = selectedCropConfig?.id
        this.areaMenus = selectedCropConfig?.regions || []
        await this.loadChartData()
        this.onAreaChange(0, false)
        this.renderTopChart()
        uni.pageScrollTo({ scrollTop: 0 })
        // 菜单切换重新拉取插屏广告
        if (this.adDisplayCount < 3) {
          handler.showSheetAd()
          this.adDisplayCount++
        }
        await this.updateScrollFixTop()
      } catch (error) {
        console.error('[ onCropChange ]', error)
        throw error
      }
    },
    onAreaChange(index = 0, scrollFixTop = true) {
      this.activeAreaIdx = index
      // 如果是东北，默认呈现的价格是玉米粒14水的价格

      if (this.isCornCrop && this.areaMenus[index]?.name === '东北') {
        const newPickerVal = [1, 16] // 默认值，玉米粒14水价格
        newPickerVal.forEach((item, index) => {
          this.$set(this.pickerValue, index, item)
        })
      }
      this.renderBottomChart()
      if (scrollFixTop) uni.pageScrollTo({ scrollTop: this.scrollFixTopValue })
    },
    onPriceTypeChange(index) {
      this.activePriceIdx = index
      uni.pageScrollTo({ scrollTop: this.scrollFixTopValue })
    },

    renderTopChart() {
      const countryLocalSeries = this.generateSeries(
        this.chartSeries,
        this.chartSeries[0],
        this.pickerValue,
        this.cropExtraPickerArgs,
        this.pickerValue[1]
      )
      const option = priceManager.getFixChartOption(this.xAxis, countryLocalSeries)
      this.updateOrCreateChart('topChartInst', this.$refs['topChartRef'], option, 1, 'top')
    },
    renderBottomChart() {
      if (!this.chartSeries || !this.chartSeries[this.activeAreaIdx]) return

      const areaChartSeries = this.generateSeries(
        [this.chartSeries[this.activeAreaIdx]],
        this.chartSeries[this.activeAreaIdx],
        this.pickerValue,
        this.cropExtraPickerArgs,
        this.pickerValue[1]
      )
      const option = priceManager.getFixChartOption(this.xAxis, areaChartSeries)
      this.updateOrCreateChart('bottomChartInst', this.$refs['bottomChartRef'], option, 0, 'bottom')
    },
    updateOrCreateChart(instanceKey, chartRef, option, showTipIndex, activeChartName) {
      if (!chartRef) return

      const lastLegendItem = option.legend.data[option.legend.data.length - 1]
      const chartInstance = this[instanceKey]

      const updateLogic = chart => {
        chart.clear()
        chart.setOption(option)
        this.showChartTip(chart, showTipIndex)
        this.initializeChart(chart, lastLegendItem, () => {
          this.activeChart = activeChartName
        })
      }

      if (!chartInstance) {
        chartRef.init(echarts).then(chart => {
          this[instanceKey] = chart
          updateLogic(chart)
        })
      } else {
        updateLogic(chartInstance)
      }
    },

    /**
     * 生成chart数据序列
     * @param {Array} seriesArr - 原始数据序列数组
     * @param {Object} currentSeries - 当前数据序列对象
     * @param {Number} pickerVal - 玉米类型选择器的值
     * @param {Array} pickerArr - 第一个选择器的选项数组
     * @param {Number} pickerValNum - 非玉米类型选择器的值
     * @returns {Array} - 生成的chart数据序列数组
     */
    generateSeries(seriesArr, currentSeries, pickerVal, pickerArr, pickerValNum) {
      const seriesData = [...seriesArr]
      if (![1, 2].includes(this.cropId)) {
        return seriesData
      }
      if (this.isCornCrop) {
        if (currentSeries.name === '进口') {
          return seriesData
        }
        seriesData.push(
          handler.calculateCornTheoryChartData({
            pickerVal,
            pickerArr,
            currentSeries
          })
        )
      } else {
        const pickerStr = cropPickerValues[pickerValNum]
        const seriesName = `${currentSeries?.name}村里-${pickerStr}（理论）`
        seriesData.push({
          name: seriesName,
          type: 'line',
          picker: true,
          data: currentSeries?.data.map(
            val =>
              // 计算理论价格：(厂价-4分) ×（100-湿度）/ 86 小麦默认村里价格等于厂价减去4分
              Math.round(((val * 1000 - 40) * (100 - parseInt(pickerStr))) / 86) / 1000
          )
        })
      }
      return seriesData
    },

    initializeChart(chart, lastLegendItem, callback) {
      const _this = this
      if (![1, 2].includes(this.cropId)) {
        return
      }
      chart.off('legendselectchanged')
      chart.on('legendselectchanged', params => {
        if (params.name === lastLegendItem) {
          chart.dispatchAction({
            type: 'legendSelect',
            name: lastLegendItem
          })
          uni.hideTabBar({
            animation: false,
            complete: () => {
              _this.isPickerPopupVisible = true
            }
          })
          if (typeof callback === 'function') {
            callback()
          }
        }
      })
      this.$once('hook:beforeDestroy', () => {
        chart.off('legendselectchanged')
      })
    },
    showChartTip(chart, index) {
      setTimeout(() => {
        chart.dispatchAction({
          type: 'showTip',
          seriesIndex: index,
          dataIndex: this.xAxis.length - 1
        })
      }, 1000)
    },
    onColumnChange(e) {
      const { value } = e.detail
      this.firstPickerValue = value[0]
      this.pickerCacheValue = value
    },
    onPickerSubmit() {
      this.pickerCacheValue.forEach((item, index) => {
        this.$set(this.pickerValue, index, item)
      })
      // this.$set(this, 'pickerValue', [...this.pickerCacheValue])

      if (this.activeChart === 'top') {
        this.renderTopChart()
      }
      if (this.activeChart === 'bottom') {
        this.renderBottomChart()
      }
      this.onPickerClose()
    },
    onPickerClose() {
      this.isPickerPopupVisible = false
    },
    // 获取元素尺寸信息
    getElementSize(selector) {
      return new Promise((resolve, reject) => {
        const query = uni.createSelectorQuery().in(this)
        query
          .select(selector)
          .boundingClientRect(data => {
            if (data) {
              resolve(data)
            } else {
              reject(new Error('获取元素尺寸失败'))
            }
          })
          .exec()
      })
    },
    // 更新滚动位置
    async updateScrollFixTop() {
      try {
        const statsCardSize = await this.getElementSize('.stats-card')
        this.scrollFixTopValue = this.adBoxHeight + statsCardSize.height + 364 + 76
      } catch (error) {
        console.error('获取元素尺寸失败:', error)
        // 设置一个默认值
        this.scrollFixTopValue = this.adBoxHeight + 528 + 76
      }
    }
  }
}
</script>

<style lang="scss">
.page_root {
  padding-bottom: 30px;
  width: 100%;
  overflow: hidden;
}
.gap {
  height: 12px;
  background: #f6f6f6;
}
.area_menu_wrapper {
  height: auto;
}
.area_menu_bar {
  position: relative;
  &.fix {
    width: 100%;
    position: fixed;
    left: 0;
    z-index: 2000;
  }
}
.factory_list_wrapper {
  border-top: 12px solid #f6f6f6;
}

.local_price_type {
  font-size: 16px;
  line-height: 32px;
  padding-right: 18px;
  text-align: right;
  margin: 10px 14px 0 0;
  position: relative;
  .btn {
    color: #ff6a01;
  }
  &::after {
    content: '';
    width: 10px;
    height: 10px;
    box-sizing: border-box;
    border-width: 0px 1px 1px 0px;
    border-style: solid;
    border-color: #ff6a01;
    position: absolute;
    margin-top: -7px;
    top: 50%;
    right: 2px;
    transform: rotate(45deg);
  }
}
.line_chart {
  width: 100%;
  height: 300px;
  margin-top: 15px;
  position: relative;
  // 以下两个块的定位值匹配精准
  // 圆圈符号：50px
  // 圆圈符号和数字：10px
  // 数字：24px
  // 两个分类之间间隙：20px
  // 减号宽度：14px
  // 数字的宽度(14)：26px
  .top_local_price_legend,
  .top_local_price_legend2,
  .bottom_local_price_legend,
  .bottom_local_price_legend2 {
    position: absolute;
    width: 165px;
    height: 24px;
    top: 0;
    left: 50%;
    // background: rgba($color: #000000, $alpha: 0.4);
    .down {
      width: 8px;
      height: 8px;
      box-sizing: border-box;
      border-width: 0 1px 1px 0;
      border-style: solid;
      border-color: #ff6a01;
      transform: rotate(45deg);
      margin: 5px 0 0 auto;
    }
    // &.big_legend {
    // 	width: 200px;
    // }
  }
  .top_local_price_legend {
    margin-left: 20px;
    @media screen and (max-width: 359px) {
      margin-left: -90px;
      top: 24px;
    }
    // &.small_legend {
    // }
    // &.big_legend {
    // 	margin-left: 2px;
    // 	@media screen and (max-width: 396) {
    // 		margin-left: -92px;
    // 		top: 24px;
    // 	}
    // }
  }
  .bottom_local_price_legend {
    margin-left: -45px;
    width: 185px;
    // &.big_legend {
    // 	margin-left: -60px;
    // }
  }
  .top_local_price_legend2 {
    width: 130px;
    margin-left: 35px;
  }
  .bottom_local_price_legend2 {
    width: 150px;
    margin-left: -30px;
  }
}
.video_ad_box {
  border-bottom: 12px solid #f6f6f6;
}
.show-popup-btn {
  display: flex;
  align-items: center;
  margin: 12px 16px;
  box-shadow: 0px 0px 6px 2px #eee;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 15px;
  font-weight: 700;
  image {
    margin-right: 16px;
  }
}

.popup-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  // background: #fff;
  // padding: 16px;
  border-radius: 13px;
  &_bg {
    width: 100%;
    height: 450px;
  }
  &_qrcode {
    position: absolute;
    top: 156px;
    width: 186px;
    height: 186px;
    border-radius: 10px;
  }
  .popup-close-btn {
    position: absolute;
    top: -38px;
    right: 0;
    z-index: 100;
  }
  &_desc {
    font-size: 16px;
    font-weight: 700;
    color: #333;
    margin-top: 8px;
    text-align: center;
  }
}
.access-module {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}
.share-alert {
  position: fixed;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  image {
    flex: none;
    width: 300px;
    height: 291px;
  }
  button {
    position: absolute;
    bottom: 46px;
    left: 0;
    right: 0;
    width: 220px;
    height: 44px;
    border: none;
  }
}
.picker-view {
  width: 100%;
  height: 600rpx;
  .item {
    line-height: 100rpx;
    text-align: center;
  }
}

.picker-action-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin: 20px 20px 0;
  button {
    width: 33%;
  }
}

.share-popup {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  border-radius: 10px;
  padding: 26px 20px;
  > div {
    font-size: 17px;
    line-height: 1.5;
    color: #666;
    &:last-child {
      margin-bottom: 20px;
    }
    text {
      font-size: 18px;
      font-weight: 700;
      color: #333;
    }
  }
  .share-popup-alert {
    font-size: 14px;
    color: #999;
    margin-bottom: 10px;
    icon {
      margin-right: 3px;
    }
  }
}
.share-popup-close-btn {
  margin-bottom: 8px;
  text-align: right;
}

.primary-btn {
  width: 100%;
  height: 44px;
  border-radius: 22px;
  background-color: #ff6a01;
  color: #fff;
  font-size: 16px;
  line-height: 44px;
  text-align: center;
  padding: 0;
}

.refresh-btn {
  display: inline-block;
  font-size: 12px;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
  border-radius: 13px;
  border-color: #ff6a01;
  color: #fff;
  background-color: #ff6a01;
  margin-left: 8px;
}
</style>
