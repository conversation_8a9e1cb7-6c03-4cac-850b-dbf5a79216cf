import messageBox from './messageBox'
import storeManager from './storeManager'
import { requestHost } from '../constant/common'
import platform from './platform'
import SECRET from '../secret'
import { performanceMonitor } from './performanceOptimizer'
const MD5 = require('md5.js')
const baseUrl = requestHost + '/api/v1'
let cookieArr = storeManager.get('session_cookie')

// 请求去重和并发控制
const pendingRequests = new Map()
const requestQueue = []
let concurrentCount = 0
const MAX_CONCURRENT = 6 // 最大并发请求数

// 请求优先级枚举
const PRIORITY = {
  HIGH: 3, // 关键数据（如设备信息）
  NORMAL: 2, // 普通请求
  LOW: 1 // 非关键数据（如统计数据）
}

// 生成请求唯一标识
function generateRequestKey(method, url, data) {
  const dataStr = data ? JSON.stringify(data) : ''
  return `${method}:${url}:${dataStr}`
}

// 请求队列管理
function processQueue() {
  if (concurrentCount >= MAX_CONCURRENT || requestQueue.length === 0) {
    return
  }

  // 按优先级排序
  requestQueue.sort((a, b) => (b.priority || PRIORITY.NORMAL) - (a.priority || PRIORITY.NORMAL))

  const request = requestQueue.shift()
  concurrentCount++

  executeRequest(request).finally(() => {
    concurrentCount--
    processQueue() // 处理下一个请求
  })
}

// 智能重试机制
async function retryRequest(requestFn, maxRetries = 3, delay = 1000) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      if (i === maxRetries - 1) throw error

      // 指数退避
      const retryDelay = delay * Math.pow(2, i)
      await new Promise(resolve => setTimeout(resolve, retryDelay))
    }
  }
}

// 根据接口传参构建签名
function buildSignQuery(data) {
  const arr = []
  if (data) {
    Object.keys(data).forEach(key => {
      arr.push({ key, value: data[key] })
    })
  }
  arr.sort((a, b) => (a.key > b.key ? -1 : 1))
  const nonce = Math.floor(Math.random() * 10000000000000000).toString(16)
  const timestamp = Date.now()
  arr.push({ key: 'nonce', value: nonce })
  arr.push({ key: 'timestamp', value: timestamp })
  const signStr =
    arr.map(item => `${item.key}=${encodeURIComponent(item.value)}`).join('&') + `&${SECRET}`
  const signature = new MD5().update(signStr).digest('hex')
  return `nonce=${nonce}&timestamp=${timestamp}&signature=${signature}`
}

// 执行实际的网络请求
function executeRequest(requestConfig) {
  const { method, url, data, config, resolve, reject } = requestConfig
  const requestKey = generateRequestKey(method, url, data)

  // 性能监控
  const startTime = Date.now()
  performanceMonitor.mark(`request_start_${requestKey}`)

  return new Promise((resolveRequest, rejectRequest) => {
    let httpUrl = `${baseUrl}${url}${url.includes('?') ? '&' : '?'}platform=${platform}`
    let header = {
      'content-type': 'application/x-www-form-urlencoded'
    }
    if (cookieArr) {
      header.cookie = cookieArr.join('; ')
    }
    if (data) {
      for (const key in data) {
        // 将 undefined 或者 null 的参数清理掉
        ;(data[key] === undefined || data[key] === null) && delete data[key]
      }
    }
    // 若某个请求需要追加加密签名
    if (config && config.needSign) {
      const signatureQuery = buildSignQuery(data)
      httpUrl += `&${signatureQuery}`
    }

    uni.request({
      url: httpUrl,
      method,
      data,
      header,
      success(res) {
        // 性能监控
        performanceMonitor.mark(`request_end_${requestKey}`)
        const duration = Date.now() - startTime
        performanceMonitor.report('http_request', {
          url,
          method,
          duration,
          success: true
        })

        // 检查cookies
        if (res.header['Set-Cookie']) {
          const newCookieArr = res.header['Set-Cookie']
            .split('; ')
            .filter(item => item.includes('session'))
            .map(item => item.replace('httponly,', ''))
          if (newCookieArr.length > 0) {
            cookieArr = newCookieArr
            storeManager.set('session_cookie', cookieArr, 'd365')
          }
        }

        const responseData = res.data
        if (responseData) {
          if (responseData.code === 1) {
            resolve(responseData.data)
            resolveRequest(responseData.data)
          } else if (responseData.code === 0) {
            const error = responseData.message || '请求异常'
            reject(error)
            rejectRequest(error)
          } else if (responseData.code === -1) {
            messageBox.alert('您的登录信息已失效，请重新登录').then(() => {
              wx.reLaunch({ url: '/pages/index/index' })
              let app = getApp()
              app.initApp(true)
            })
          } else {
            reject('请求异常')
            rejectRequest('请求异常')
          }
        } else {
          reject('请求异常')
          rejectRequest('请求异常')
        }
      },
      fail(err) {
        // 性能监控
        const duration = Date.now() - startTime
        performanceMonitor.report('http_request', {
          url,
          method,
          duration,
          success: false,
          error: err
        })

        reject('请求异常')
        rejectRequest(err)
      }
    })
  }).finally(() => {
    // 请求完成后从pending中移除
    pendingRequests.delete(requestKey)
  })
}

function http(method, url, data, config = {}) {
  return new Promise(function (resolve, reject) {
    const requestKey = generateRequestKey(method, url, data)

    // 请求去重：如果相同请求正在进行中，直接返回该请求的Promise
    if (pendingRequests.has(requestKey)) {
      return pendingRequests.get(requestKey)
    }

    const requestConfig = {
      method,
      url,
      data,
      config,
      resolve,
      reject,
      priority: config.priority || PRIORITY.NORMAL
    }

    // 创建请求Promise并缓存
    const requestPromise = new Promise((resolveRequest, rejectRequest) => {
      requestConfig.resolveRequest = resolveRequest
      requestConfig.rejectRequest = rejectRequest

      // 如果启用重试机制
      if (config.retry !== false) {
        const maxRetries = config.maxRetries || 3
        retryRequest(() => executeRequest(requestConfig), maxRetries)
          .then(resolveRequest)
          .catch(rejectRequest)
      } else {
        // 添加到队列或直接执行
        if (concurrentCount < MAX_CONCURRENT) {
          concurrentCount++
          executeRequest(requestConfig)
            .then(resolveRequest)
            .catch(rejectRequest)
            .finally(() => {
              concurrentCount--
              processQueue()
            })
        } else {
          requestQueue.push(requestConfig)
        }
      }
    })

    pendingRequests.set(requestKey, requestPromise)
    return requestPromise
  })
}

function upload(url, filePath, data) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: baseUrl + url,
      filePath,
      name: 'file',
      formData: data || {},
      success(res) {
        const data = JSON.parse(res.data)
        if (data.code === 1) {
          resolve(data.data)
        } else {
          reject(data.message || '上传失败')
        }
      },
      fail(err) {
        reject(err)
      }
    })
  })
}

;['OPTIONS', 'GET', 'HEAD', 'POST', 'PUT', 'DELETE', 'TRACE', 'CONNECT'].forEach(method => {
  http[method.toLocaleLowerCase()] = function (url, data, config) {
    return http(method, url, data, config)
  }
})

http.upload = upload
http.PRIORITY = PRIORITY

export default http
export { PRIORITY }
