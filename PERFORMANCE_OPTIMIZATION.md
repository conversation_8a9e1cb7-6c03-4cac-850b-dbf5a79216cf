# uniapp 应用架构性能优化方案

本文档旨在对现有 uniapp 项目的 `App.vue` 进行架构层面的重构，以追求极致的启动性能和可维护性，同时确保现有业务逻辑不变。

## 1. 核心问题分析

当前 `App.vue` 的实现存在几个关键问题，影响了性能和后续的开发效率：

1.  **启动时序阻塞**: 应用的渲染和核心功能（如登录）强依赖于 `onLaunch` 中多个网络请求的完成，导致白屏时间过长。
2.  **全局状态管理混乱**: `globalData` 和 `Vuex` 混用，`globalData` 是非响应式的，导致数据流不清晰，状态更新困难且容易出错。
3.  **职责耦合过重**: `App.vue` 承担了数据获取、状态管理、登录逻辑等多重职责，违反了“单一职责原则”，代码难以维护和扩展。

## 2. 优化目标

1.  **降低 TTI (Time to Interactive)**: 尽快让用户看到页面并可以进行操作。
2.  **统一状态管理**: 建立一个单一、可预测、响应式的全局状态数据源。
3.  **代码解耦**: 提高代码的模块化、可读性和可维护性。

## 3. 详细优化方案

### 步骤一：统一状态管理 (Vuex/Pinia)

将所有 `globalData` 中的状态迁移至 `Vuex`，并按模块划分。

**建议的 Vuex Store 结构:**

- **`user` 模块**: 管理 `userInfo`, `isLogin`, `inviteUserId`。
- **`app` 模块**: 管理 `launchIng`, `deviceInfo`, `sysConfig`, `openCount` 等应用级状态。
- **`data` 模块**: 管理 `crops`, `cropsMap`, `cityArr`, `collectionsMap` 等业务数据。

**优势**:

- **响应式**: 状态变更后，所有依赖该状态的组件将自动更新。
- **可预测性**: 严格的单向数据流，通过 `mutations` 和 `actions` 修改状态，便于追踪和调试。
- **组织性**: 模块化结构使代码更清晰。

### 步骤二：抽离业务逻辑

将 `App.vue` 中的逻辑抽离到独立的 `services` 或 `hooks` 中。

1.  **创建 `src/services/appLaunchService.js`**:

    - 封装 `onLaunch` 中的所有核心逻辑，包括处理分享参数、并行获取初始化数据、用户登录等。
    - `App.vue` 只负责调用该 service 的主函数。

2.  **重构 `api` 目录**:
    - 当前的 `xxManager.js` 命名方式更像是类的实例管理。可以考虑将其重构为更符合 `api` 职能的纯函数模块，例如 `src/api/config.js`, `src/api/user.js`。

### 步骤三：优化启动流程 (核心)

这是性能优化的关键。目标是**“非阻塞渲染”**，让页面尽快展示，同时在后台进行数据加载和登录。

**新的启动流程设计:**

```mermaid
graph TD
    A[App onLaunch 启动] --> B{启动加载动画};
    B --> C{处理必要的同步启动参数};
    C --> D[立即跳转至首页/目标页];
    D --> E{页面展示骨架屏 Skeleton};

    subgraph 后台并行任务
        C --> F[异步: 初始化登录流程];
        C --> G[异步: 获取系统配置];
        C --> H[异步: 获取城市/农作物数据];
    end

    F --> I{登录成功};
    G --> J{配置获取成功};
    H --> K{业务数据获取成功};

    I --> L[更新 Vuex 用户状态];
    J --> M[更新 Vuex 配置状态];
    K --> N[更新 Vuex 业务数据状态];

    subgraph 页面响应
        L --> O{页面根据登录状态显示内容};
        M --> P{页面根据配置显示内容};
        N --> Q{页面根据业务数据显示内容};
    end

    O & P & Q --> R[骨架屏消失, 渲染真实数据];
```

**实施要点**:

1.  **`onLaunch` 最小化**: `onLaunch` 只做最少量、最必要的工作（如处理 `options` 参数），然后立即让应用继续渲染流程。
2.  **后台静默加载**: 将数据获取和登录过程放入后台执行，不阻塞 UI 线程。
3.  **页面骨架屏 (Skeleton)**: 在等待异步数据返回时，页面级应使用骨架屏来提供积极的用户反馈，避免白屏。
4.  **数据懒加载/分级**:
    - **核心数据**: 影响首屏渲染的数据（如 `sysConfig`）应最先加载。
    - **非核心数据**: 其他页面的数据（如 `collectionsMap`）可以在用户访问到对应页面时再进行加载（懒加载）。
5.  **登录与业务解耦**: 登录状态不应成为查看公开内容的先决条件。用户可以在未登录状态下浏览，当进行需要权限的操作时，再引导其登录。

## 4. 实施计划

1.  **[x] 梳理 `globalData`，将所有全局状态迁移至 `vuex`**
2.  **[-] 抽离 `App.vue` 中的业务逻辑到独立的 `services` 或 `hooks`**
3.  **[ ] 优化启动流程，实现非阻塞的并行数据获取和懒加载**
4.  **[x] 创建 `plan.md` 详细阐述架构设计和优化细节**
5.  **[ ] 请求用户审查计划并切换至“代码”模式进行实施**

---

此方案将显著提升应用的启动速度和用户体验，并为未来的功能迭代打下坚实、可维护的架构基础。
