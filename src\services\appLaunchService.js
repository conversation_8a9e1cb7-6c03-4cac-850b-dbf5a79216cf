/**
 * 应用启动服务
 * 负责应用启动时的数据初始化和用户登录流程
 */

import userManager from '../api/userManager'
import logManager from '../api/logManager'
import pointsManager from '../api/pointsManager'
import orderManager from '../api/orderManager'
import cityManager from '../api/cityManager'
import configManager from '../api/configManager'
import qrcodeManager from '../api/qrcodeManager'
import { OPEN_WX_MINI_APP } from '../constant/event'
import { performanceMonitor, cacheOptimizer } from '../utils/performanceOptimizer'
import storeManager from '../utils/storeManager'
import { PRIORITY } from '../utils/http'

class AppLaunchService {
  constructor() {
    this.isLaunching = false
    this.launchPromise = null
    this.criticalDataLoaded = false
    this.nonCriticalDataLoaded = false
  }

  /**
   * 启动应用
   * @param {Object} options 启动参数
   * @param {Object} store Vuex store
   * @param {Object} globalData 全局数据对象
   */
  async launch(options, store, globalData) {
    if (this.isLaunching) {
      return this.launchPromise
    }

    this.isLaunching = true
    performanceMonitor.mark('app_launch_start')

    this.launchPromise = this._executeLaunch(options, store, globalData)
    
    try {
      await this.launchPromise
    } finally {
      this.isLaunching = false
      performanceMonitor.mark('app_launch_end')
      const launchTime = performanceMonitor.measure('app_launch_start', 'app_launch_end')
      performanceMonitor.report('app_launch', { duration: launchTime })
    }

    return this.launchPromise
  }

  /**
   * 执行启动流程
   * @private
   */
  async _executeLaunch(options, store, globalData) {
    // 阶段1: 处理启动参数（同步，必须完成）
    this._processLaunchOptions(options, globalData)

    // 阶段2: 加载关键数据（异步，但需要等待）
    await this._loadCriticalData(store, globalData)

    // 阶段3: 启动登录流程（异步，不阻塞）
    this._startLoginProcess(globalData, store)

    // 阶段4: 加载非关键数据（异步，后台进行）
    this._loadNonCriticalData(store, globalData)

    // 阶段5: 完成启动
    this._finishLaunch(globalData)
  }

  /**
   * 处理启动参数
   * @private
   */
  _processLaunchOptions(options, globalData) {
    performanceMonitor.mark('process_options_start')
    
    const { shareUid, shareId } = options?.query || {}
    
    if (shareUid) {
      globalData.inviteUserId = shareUid
    }

    // 处理分享ID（异步处理，不阻塞启动）
    if (shareId) {
      this._handleShareId(shareId).catch(err => {
        console.error('处理分享ID失败:', err)
      })
    }

    performanceMonitor.mark('process_options_end')
  }

  /**
   * 加载关键数据（影响UI渲染的数据）
   * @private
   */
  async _loadCriticalData(store, globalData) {
    performanceMonitor.mark('critical_data_start')

    try {
      // 只加载影响UI渲染的关键数据
      const deviceInfo = await configManager.getDeviceInfo()
      globalData.deviceInfo = deviceInfo

      // 获取系统配置（高优先级）
      const sysConfig = await cacheOptimizer.getWithBackgroundRefresh(
        'system_config',
        () => configManager.fetchSystemConfig(),
        1000 * 60 * 30, // 30分钟缓存
        storeManager
      )
      
      store.commit('sysConfig', sysConfig)

      this.criticalDataLoaded = true
      performanceMonitor.mark('critical_data_end')
    } catch (error) {
      console.error('加载关键数据失败:', error)
      throw error
    }
  }

  /**
   * 启动登录流程（非阻塞）
   * @private
   */
  _startLoginProcess(globalData, store) {
    performanceMonitor.mark('login_start')

    // 异步执行登录，不阻塞启动
    setTimeout(async () => {
      try {
        if (!userManager.checkLogin()) {
          await this._performLogin(globalData, store)
        } else {
          store.commit('isLogin', true)
          this._onLoginSuccess(globalData)
        }
      } catch (error) {
        console.error('登录失败:', error)
      } finally {
        performanceMonitor.mark('login_end')
      }
    }, 0)
  }

  /**
   * 执行登录
   * @private
   */
  async _performLogin(globalData, store) {
    return new Promise((resolve, reject) => {
      uni.login({
        success: async (res) => {
          try {
            const code = res.code
            const { inviteUserId } = globalData
            const user = await userManager.miniLoginByCode(code, inviteUserId)
            
            globalData.userInfo = user
            store.commit('isLogin', true)
            this._onLoginSuccess(globalData)
            resolve(user)
          } catch (err) {
            reject(err)
          }
        },
        fail: reject
      })
    })
  }

  /**
   * 加载非关键数据（后台加载）
   * @private
   */
  _loadNonCriticalData(store, globalData) {
    // 延迟加载非关键数据，避免阻塞启动
    setTimeout(async () => {
      performanceMonitor.mark('non_critical_data_start')

      try {
        // 并行加载非关键数据
        const [cropsInfo, cityArr] = await Promise.all([
          cacheOptimizer.getWithBackgroundRefresh(
            'crops_info',
            () => orderManager.fetchCrops(),
            1000 * 60 * 60, // 1小时缓存
            storeManager
          ),
          cacheOptimizer.getWithBackgroundRefresh(
            'city_array',
            () => cityManager.fetchAllCities(),
            1000 * 60 * 60 * 24, // 24小时缓存
            storeManager
          )
        ])

        // 更新全局数据
        Object.assign(globalData, cropsInfo)
        globalData.cityArr = cityArr

        // 更新打开次数
        const openCount = configManager.getOpenCount() + 1
        globalData.openCount = openCount
        configManager.setOpenCount(openCount)

        this.nonCriticalDataLoaded = true
        performanceMonitor.mark('non_critical_data_end')
      } catch (error) {
        console.error('加载非关键数据失败:', error)
      }
    }, 100) // 100ms延迟，让关键渲染先完成
  }

  /**
   * 处理分享ID
   * @private
   */
  async _handleShareId(shareId) {
    try {
      const res = await pointsManager.checkShareAndAssistance({ id: shareId })
      // 可以在这里处理分享结果
      console.log('分享处理结果:', res)
    } catch (error) {
      console.error('处理分享失败:', error)
    }
  }

  /**
   * 登录成功回调
   * @private
   */
  _onLoginSuccess(globalData) {
    // 添加日志记录
    logManager.addLog(OPEN_WX_MINI_APP)
    logManager.addStartRecord(globalData.inviteUserId)

    // 触发当前页面的登录回调
    this._triggerPageLoginCallback()
  }

  /**
   * 触发页面登录回调
   * @private
   */
  _triggerPageLoginCallback() {
    const pages = getCurrentPages()
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      if (currentPage.$vm && currentPage.$vm.onLoginSuccess) {
        currentPage.$vm.onLoginSuccess()
      }
    } else {
      // 页面还未实例化，延迟执行
      setTimeout(() => this._triggerPageLoginCallback(), 20)
    }
  }

  /**
   * 完成启动
   * @private
   */
  _finishLaunch(globalData) {
    globalData.launchIng = false
    uni.hideLoading()
    
    // 清理过期缓存
    setTimeout(() => {
      storeManager.cleanExpired()
    }, 5000)
  }

  /**
   * 获取启动状态
   */
  getStatus() {
    return {
      isLaunching: this.isLaunching,
      criticalDataLoaded: this.criticalDataLoaded,
      nonCriticalDataLoaded: this.nonCriticalDataLoaded
    }
  }

  /**
   * 重新启动（用于重新登录等场景）
   */
  async restart(options, store, globalData) {
    this.criticalDataLoaded = false
    this.nonCriticalDataLoaded = false
    return this.launch(options, store, globalData)
  }
}

// 导出单例
export default new AppLaunchService()
