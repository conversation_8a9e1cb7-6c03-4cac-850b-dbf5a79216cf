import http, { PRIORITY } from '../utils/http'
import storeManager from '../utils/storeManager'
import { cacheOptimizer } from '../utils/performanceOptimizer'
const tradePriceShowKey = 'trade_price_show_key'

function getOpenCount() {
  return storeManager.get(tradePriceShowKey) || 0
}
function setOpenCount(value) {
  storeManager.set(tradePriceShowKey, value, 'd365') || 0
}

function getSysConfig() {
  return storeManager.get('systemConfig')
}
function setSysConfig(data) {
  storeManager.set('systemConfig', data)
}

function queryConfig() {
  return http.get('/api/nz/config/mini')
}

function fetchSystemConfig() {
  return new Promise(function (resolve, reject) {
    http
      .get('/common/sys/config', null, { priority: PRIORITY.HIGH })
      .then(data => {
        setSysConfig(data)
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

// 智能获取系统配置（带缓存）
function getSystemConfigSmart() {
  return cacheOptimizer.getWithBackgroundRefresh(
    'system_config_smart',
    fetchSystemConfig,
    1000 * 60 * 30, // 30分钟缓存
    storeManager
  )
}

function getDeviceInfo() {
  return new Promise(function (resolve, reject) {
    // 先检查缓存
    const cached = storeManager.get('device_info')
    if (cached) {
      resolve(cached)
      return
    }

    const app = getApp()
    if (app && app.globalData.deviceInfo) {
      resolve(app.globalData.deviceInfo)
    } else {
      wx.getSystemInfo({
        success: res => {
          const { statusBarHeight, screenHeight, safeArea, platform } = res
          let navBarHeight = platform === 'android' ? 48 : 44
          // menu为胶囊，判断是否能读到胶囊位置，读到胶囊说明高度需要根据胶囊重新计算
          const menuBounding = wx.getMenuButtonBoundingClientRect()
          if (menuBounding) {
            const { height, top } = menuBounding
            navBarHeight = height + (top - statusBarHeight) * 2
          }
          const deviceInfo = {
            menuBounding,
            statusBarHeight, // 状态栏高度
            navBarHeight, // 导航栏高度
            bottomInsetSafeArea: screenHeight - safeArea.bottom,
            totalBarHeight: statusBarHeight + navBarHeight // 状态栏加导航栏
          }

          // 缓存设备信息（1天）
          storeManager.set('device_info', deviceInfo, 'd1')
          resolve(deviceInfo)
        },
        fail(err) {
          reject(err)
        }
      })
    }
  })
}

function uploadWxFile(filePath, namespace) {
  return http.upload('/common/files/wx-uploads', filePath, { namespace })
}

export default {
  getOpenCount,
  setOpenCount,
  queryConfig,
  fetchSystemConfig,
  getSystemConfigSmart,
  getDeviceInfo,
  getSysConfig,
  uploadWxFile
}
