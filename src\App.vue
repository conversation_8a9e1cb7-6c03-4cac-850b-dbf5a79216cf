<script>
import appLaunchService from './services/appLaunchService'
import { performanceMonitor } from './utils/performanceOptimizer'
import { cropArr } from './constant/common'

export default {
  globalData: {
    cropArr,
    code: null,
    launchIng: true,
    userInfo: {},
    orderInfo: {},
    newOrder: {},
    inviteUserId: null,
    crops: [],
    cropsMap: {},
    collectionsMap: {},
    deviceInfo: null
  },
  async onLaunch(options) {
    // 显示启动加载
    uni.showLoading({ title: '启动中' })

    // 性能监控
    performanceMonitor.mark('app_onlaunch_start')

    try {
      // 使用新的启动服务
      await appLaunchService.launch(options, this.$store, this.globalData)
    } catch (err) {
      console.error('App启动失败:', err)
      // 即使启动失败也要隐藏loading，让用户能看到错误页面
      uni.hideLoading()
      this.globalData.launchIng = false
    } finally {
      performanceMonitor.mark('app_onlaunch_end')
      const launchTime = performanceMonitor.measure('app_onlaunch_start', 'app_onlaunch_end')
      console.log(`App onLaunch 耗时: ${launchTime}ms`)
    }
  },
  methods: {
    // 重新启动应用（用于重新登录等场景）
    async restartApp(options = {}) {
      try {
        await appLaunchService.restart(options, this.$store, this.globalData)
      } catch (err) {
        console.error('重启应用失败:', err)
      }
    },

    // 获取启动状态
    getLaunchStatus() {
      return appLaunchService.getStatus()
    }
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  }
}
</script>

<style lang="scss">
@import './app.scss';
</style>
