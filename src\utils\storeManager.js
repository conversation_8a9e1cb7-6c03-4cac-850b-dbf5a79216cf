import { stringToTime } from '../utils/date'

// 缓存统计
const cacheStats = {
  hits: 0,
  misses: 0,
  sets: 0
}

// LRU缓存管理
const lruCache = new Map()
const MAX_CACHE_SIZE = 100

function updateLRU(key) {
  if (lruCache.has(key)) {
    // 移动到最后（最近使用）
    const value = lruCache.get(key)
    lruCache.delete(key)
    lruCache.set(key, value)
  } else {
    lruCache.set(key, Date.now())

    // 如果超过最大缓存数量，删除最久未使用的
    if (lruCache.size > MAX_CACHE_SIZE) {
      const firstKey = lruCache.keys().next().value
      lruCache.delete(firstKey)
      clear(firstKey)
    }
  }
}

function set(key, value, expires) {
  expires = expires || 'h4' // 默认缓存4小时内有效
  const maxAge = Date.now() + stringToTime(expires)

  try {
    wx.setStorageSync(key, {
      data: value,
      expires: maxAge,
      createdAt: Date.now(),
      accessCount: 0
    })

    updateLRU(key)
    cacheStats.sets++
  } catch (error) {
    console.error('缓存设置失败:', key, error)
  }
}

function get(key) {
  try {
    const storeData = wx.getStorageSync(key)
    if (!storeData) {
      cacheStats.misses++
      return null
    }

    const { data, expires, accessCount = 0 } = storeData
    if (data && expires && expires > Date.now()) {
      // 更新访问统计
      storeData.accessCount = accessCount + 1
      storeData.lastAccess = Date.now()
      wx.setStorageSync(key, storeData)

      updateLRU(key)
      cacheStats.hits++
      return data
    } else {
      // 缓存过期，清理
      clear(key)
      cacheStats.misses++
      return null
    }
  } catch (error) {
    console.error('缓存获取失败:', key, error)
    cacheStats.misses++
    return null
  }
}

function clear(key) {
  try {
    wx.removeStorageSync(key)
    lruCache.delete(key)
  } catch (error) {
    console.error('缓存清理失败:', key, error)
  }
}

function clearAll() {
  try {
    wx.clearStorage()
    lruCache.clear()
    // 重置统计
    cacheStats.hits = 0
    cacheStats.misses = 0
    cacheStats.sets = 0
  } catch (error) {
    console.error('清理所有缓存失败:', error)
  }
}

// 获取缓存统计信息
function getStats() {
  const hitRate =
    cacheStats.hits + cacheStats.misses > 0
      ? ((cacheStats.hits / (cacheStats.hits + cacheStats.misses)) * 100).toFixed(2)
      : 0

  return {
    ...cacheStats,
    hitRate: `${hitRate}%`,
    cacheSize: lruCache.size
  }
}

// 清理过期缓存
function cleanExpired() {
  try {
    const info = wx.getStorageInfoSync()
    const keys = info.keys || []
    let cleanedCount = 0

    keys.forEach(key => {
      const storeData = wx.getStorageSync(key)
      if (storeData && storeData.expires && storeData.expires <= Date.now()) {
        clear(key)
        cleanedCount++
      }
    })

    console.log(`清理了 ${cleanedCount} 个过期缓存`)
    return cleanedCount
  } catch (error) {
    console.error('清理过期缓存失败:', error)
    return 0
  }
}

// 获取缓存大小信息
function getCacheSize() {
  try {
    const info = wx.getStorageInfoSync()
    return {
      currentSize: info.currentSize,
      limitSize: info.limitSize,
      keys: info.keys?.length || 0
    }
  } catch (error) {
    console.error('获取缓存大小失败:', error)
    return null
  }
}

export default {
  set,
  get,
  clear,
  clearAll,
  getStats,
  cleanExpired,
  getCacheSize
}
