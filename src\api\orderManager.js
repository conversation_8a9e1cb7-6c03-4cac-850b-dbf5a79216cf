import http, { PRIORITY } from '../utils/http'
import storeManager from '../utils/storeManager'
import { cacheOptimizer } from '../utils/performanceOptimizer'
const STORE_KEY = 'order_manager_v2'
let cropsInfo = storeManager.get(STORE_KEY + 'cropsInfo')
let submitOrder
let checkDetailOrder
let orderBuyersMap

function setSubmitOrder(order) {
  submitOrder = order
}
function getSubmitOrder() {
  return submitOrder
}
function setCheckDetailOrder(order) {
  checkDetailOrder = order
}
function getCheckDetailOrder() {
  return checkDetailOrder
}

function getOrderBuyersMap() {
  return orderBuyersMap
}

function fetchCrops() {
  if (cropsInfo) {
    return Promise.resolve(cropsInfo)
  } else {
    return queryCrops()
  }
}

// 智能获取农作物数据（带缓存）
function fetchCropsSmart() {
  return cacheOptimizer.getWithBackgroundRefresh(
    'crops_info_smart',
    queryCrops,
    1000 * 60 * 60, // 1小时缓存
    storeManager
  )
}

function orderPhotoFormat(item) {
  Object.keys(item).forEach(key => {
    if (key.match(/photo|video|Photo|Video/)) {
      item[key] = item[key].split(',')
    }
  })
  return item
}

function mapOrder(orders) {
  return orders.map(item => orderPhotoFormat(item))
}

function queryCrops() {
  return http.get('/common/crop/all').then(data => {
    const { crops, collections } = data
    const cropsMap = {}
    const collectionsMap = {}
    collections.forEach(item => {
      if (item.options) {
        item.optionsArr = item.options.split(',').map(item => {
          return {
            value: item,
            checked: false
          }
        })
      }
    })
    // 将 农作物的map、收集信息的map 存放在全局变量
    collections.forEach(item => {
      collectionsMap[item.id] = item
    })

    const cropsOrderOnline = [] // 价格页已经上线的作物
    crops.forEach(item => {
      let collectionsArr = []
      if (item.collectionIds) {
        collectionsArr = item.collectionIds.split(',').map(id => collectionsMap[id])
      }
      item.collectionsArr = collectionsArr
      cropsMap[item.id] = item
      if (item.online) {
        cropsOrderOnline.push(item)
      }
    })

    // 农作物按照权重weight 从大到小排序
    crops.sort((a, b) => b.sortNumber - a.sortNumber)
    cropsInfo = {
      crops,
      cropsOrderOnline,
      cropsMap,
      collectionsMap
    }
    storeManager.set(STORE_KEY + 'cropsInfo', cropsInfo, 'm10')
    return cropsInfo
  })
}

function createOrder(orderInfo) {
  let newData = {}
  Object.keys(orderInfo).forEach(key => {
    if (key.match(/photo|video|Photo|Video/)) {
      newData[key] = orderInfo[key].join(',')
    }
  })
  return http.post('/order/add', Object.assign({}, orderInfo, newData))
}

// 查询我的订单
function queryMyOrders(status) {
  return http.post('/order/my', { status }).then(res => mapOrder(res))
}

// 查询首页的订单
function queryHomeOrders(pageIndex, pageSize) {
  return http.post('/order/list', { pageIndex, pageSize }).then(res => {
    res.list = mapOrder(res.list)
    return res
  })
}

// 查询首页的订单
function queryHomeTradeOrders(pageIndex, pageSize) {
  return http.post('/order/tradeList', { pageIndex, pageSize }).then(res => {
    res.list.forEach(trade => {
      trade.order = orderPhotoFormat(trade.order)
    })
    res.list = mapOrder(res.list)
    return res
  })
}

function queryTradeById(id) {
  return http.get('/order/tradeDetail/' + id).then(trade => {
    trade.order = orderPhotoFormat(trade.order)
    return trade
  })
}
// 查询周边的订单
function queryNearbyOrders(longitude, latitude, pageIndex, pageSize) {
  return http.post('/order/nearbyList', { longitude, latitude, pageIndex, pageSize }).then(res => {
    res.list = mapOrder(res.list)
    return res
  })
}

function queryMyLikeOrders() {
  return http.post('/order/like').then(res => {
    res = mapOrder(res)
    return res
  })
}

function addMobileClickLog(orderId) {
  return http.get('/order/relation/add/telephone/' + orderId)
}

function addOrderViewLog(orderId) {
  return http.get('/order/relation/add/view/' + orderId)
}

//上传订单相关照片
function uploadOrderPhoto(path) {
  return http.upload('/common/upload/photo', path)
}

//上传订单相关视频
function uploadOrderVideo(path) {
  return http.upload('/common/upload/video', path)
}

function cancelOrder(id) {
  return http.put(`/order/cancel/${id}`)
}

// 查询待收集信息订单
function queryCollectOrders(pageIndex, pageSize) {
  return http.post('/order/collector/askList', { pageIndex, pageSize }).then(res => {
    res.list = mapOrder(res.list)
    return res
  })
}

function updateOrder(id, updateData) {
  return http.put(`/order/update/${id}`, updateData)
}

function updateOrderByCollector(id, updateData) {
  return http.put(`/order/collector/update/${id}`, updateData)
}

function queryOrderById(id) {
  return http.get(`/order/detail/${id}`).then(data => orderPhotoFormat(data))
}

function refreshOrder(id) {
  return http.put(`/order/refresh/${id}`)
}

function stopOrder(id) {
  return http.put(`/order/stop/${id}`)
}

function deleteOrder(id) {
  return http.delete(`/order/delete/${id}`)
}

function activeOrder(id) {
  return http.put(`/order/active/${id}`)
}

function addLikeOrder(id) {
  return http.get(`/order/addLike/${id}`)
}

function cancelLikeOrder(id) {
  return http.get(`/order/cancelLike/${id}`)
}

function addCollectAsk(id) {
  return http.get(`/order/addAsk/${id}`)
}

function queryOrderRelations(id) {
  return http.get(`/order/relation/list/${id}`)
}

function submitTrade(orderId, tradeData) {
  return http.post('/order/addTrade/' + orderId, tradeData)
}

function showSubscribe() {
  return new Promise(resolve => {
    const key = 'O1kkT2VVE7Uw9Jk12qKVz_7T7MPq7YHUFz1V2sWRBBY'
    wx.requestSubscribeMessage({
      tmplIds: [key],
      success(res) {
        resolve(res[key])
      },
      fail(err) {
        resolve('error')
      }
    })
  })
}

export default {
  refreshOrder,
  stopOrder,
  deleteOrder,
  activeOrder,
  setSubmitOrder,
  getSubmitOrder,
  setCheckDetailOrder,
  getCheckDetailOrder,
  getOrderBuyersMap,
  fetchCrops,
  fetchCropsSmart,
  queryCrops,
  createOrder,
  queryMyOrders,
  queryHomeOrders,
  queryHomeTradeOrders,
  queryTradeById,
  queryNearbyOrders,
  queryMyLikeOrders,
  cancelOrder,
  addLikeOrder,
  cancelLikeOrder,
  addCollectAsk,
  queryOrderRelations,
  uploadOrderPhoto,
  uploadOrderVideo,
  queryCollectOrders,
  updateOrder,
  updateOrderByCollector,
  queryOrderById,
  submitTrade,
  addMobileClickLog,
  addOrderViewLog,
  showSubscribe
}
